---
import Layout from '~/layouts/PageLayout.astro';
import Hero from '~/components/widgets/Hero.astro';
import Content from '~/components/widgets/Content.astro';
import Features from '~/components/widgets/Features.astro';
import Steps from '~/components/widgets/Steps.astro';
import Pricing from '~/components/widgets/Pricing.astro';
import ContactMethods from '~/components/widgets/ContactMethods.astro';
import CustomStyles from '~/components/CustomStyles.astro';
import heroImage from '~/assets/images/ycsj.webp';
const metadata = {
  title: '远程刷机服务 - 领创工作室',
  description: '领创工作室提供专业的远程刷机服务，支持各种Android设备，安全可靠，价格合理。专业技术团队为您提供一对一服务。',
  keywords: '远程刷机,Android刷机,手机刷机,ROM刷入,系统刷机,领创工作室',
};

// 服务特色
const serviceFeatures = [
  {
    title: '安全可靠',
    description: '采用专业刷机工具和流程，确保设备安全，降低变砖风险。',
    icon: 'tabler:shield-check',
  },
  {
    title: '支持广泛',
    description: '支持主流Android设备，包括小米、华为、OPPO、vivo等品牌。',
    icon: 'tabler:device-mobile',
  },
  {
    title: '专业团队',
    description: '经验丰富的技术团队，熟悉各种设备的刷机流程和注意事项。',
    icon: 'tabler:users',
  },
  {
    title: '售后保障',
    description: '提供刷机后的技术支持，确保系统稳定运行。',
    icon: 'tabler:headset',
  },
];

// 刷机流程
const flashingSteps = [
  {
    title: '咨询预约',
    description: '联系我们的客服，说明您的设备型号和刷机需求，我们会为您评估可行性和报价。',
    icon: 'tabler:message-circle',
  },
  {
    title: '设备检测',
    description: '通过远程工具检测您的设备状态，确认设备信息和当前系统版本。',
    icon: 'tabler:search',
  },
  {
    title: '备份数据',
    description: '协助您备份重要数据，包括联系人、照片、应用等，确保数据安全。',
    icon: 'tabler:database',
  },
  {
    title: '执行刷机',
    description: '使用专业工具远程执行刷机操作，全程监控刷机进度，确保成功完成。',
    icon: 'tabler:cpu',
  },
  {
    title: '系统配置',
    description: '刷机完成后，协助您进行系统初始化配置，恢复必要的应用和数据。',
    icon: 'tabler:settings',
  },
  {
    title: '测试验收',
    description: '全面测试设备功能，确保系统稳定运行，交付给您使用。',
    icon: 'tabler:check',
  },
];


// 联系方式数据
const contactMethods = [
  {
    title: '微信咨询',
    description: '扫码添加微信，获取一对一专业咨询服务，支持语音通话',
    icon: 'tabler:brand-wechat',
    qrCode: '/images/qrcodes/qr-wechat.webp', // 需要添加微信二维码图片
    link: 'https://lacs.cc/wechat', // 微信联系页面
    linkText: '添加微信',
    color: 'text-green-600',
    bgColor: 'bg-green-50',
  },
  {
    title: 'QQ咨询',
    description: '点击直接跳转QQ聊天，快速获得技术支持和在线答疑',
    icon: 'tabler:brand-qq',
    qrCode: '/images/qrcodes/qq-qr.webp', // 需要添加QQ二维码图片
    link: 'https://qm.qq.com/q/9myAkzwVY4', // QQ联系页面
    linkText: '联系QQ',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
  },
  {
    title: '闲鱼店铺',
    description: '访问我们的闲鱼店铺，查看真实用户评价和服务案例',
    icon: 'tabler:shopping-cart',
    qrCode: '/images/qrcodes/xianyu.webp', // 需要添加闲鱼二维码图片
    link: 'https://m.tb.cn/h.hoyOrYl?tk=1mnp4TPgpjd', // 闲鱼店铺页面
    linkText: '访问店铺',
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
  },
];

---

<Layout metadata={metadata}>
  <CustomStyles />
  
  <!-- Hero Section -->
  <Hero
    tagline="远程刷机服务"
    title="专业安全的Android设备刷机服务"
    subtitle="领创工作室提供专业的远程刷机服务，支持各种Android设备。我们拥有丰富的刷机经验和专业的技术团队，为您提供安全、可靠的刷机解决方案。"
    actions={[
      {
        variant: 'primary',
        text: '立即咨询',
        href: '#contact',
        target: '_blank',
        icon: 'tabler:message-circle',
      },
      {
        variant: 'secondary',
        text: '了解流程',
        href: '#process',
        icon: 'tabler:arrow-down',
      },
    ]}
        image={{ src: heroImage, alt: '展示' }}
  />
  <!-- 联系方式-->
  <ContactMethods
    id='contact'
    title="联系我们"
    subtitle="多种联系方式，选择最适合您的沟通方式，我们的团队将尽快与您联系。"
    tagline="联系方式"
    contactMethods={contactMethods}
  >
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-gray-50 dark:bg-slate-800"></div>
    </Fragment>
  </ContactMethods>
  

  <!-- 服务特色 -->
  <Features
    title="为什么选择我们的远程刷机服务"
    subtitle="专业、安全、可靠的刷机服务，让您的设备焕然一新"
    tagline="服务特色"
    items={serviceFeatures}
    columns={4}
  >
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-slate-800"></div>
    </Fragment>
  </Features>

  <!-- 刷机流程 -->
  <Steps
    id="process"
    title="专业的刷机服务流程"
    subtitle="我们遵循标准化的刷机流程，确保每一步都安全可控"
    tagline="服务流程"
    items={flashingSteps}
  />

  <!-- 注意事项 -->
  <Content
    title="刷机前的重要提醒"
    subtitle="请仔细阅读以下注意事项，确保刷机过程顺利进行"
    items={[
      {
        title: '数据备份',
        description: '刷机前请务必备份重要数据，包括联系人、照片、文档等。',
        icon: 'tabler:database-export',
      },
      {
        title: '设备状态',
        description: '确保设备电量充足（建议50%以上），网络连接稳定。',
        icon: 'tabler:battery-charging',
      },
      {
        title: '风险提醒',
        description: '刷机存在一定风险，可能导致设备变砖，请谨慎考虑。',
        icon: 'tabler:alert-triangle',
      },
      {
        title: '保修影响',
        description: '刷机可能影响设备保修，请在保修期外或确认后再进行。',
        icon: 'tabler:shield-x',
      },
    ]}
  >
    <Fragment slot="content">
      <h3 class="text-2xl font-bold tracking-tight dark:text-white sm:text-3xl mb-2">
        刷机须知
      </h3>
      刷机是一项技术性操作，虽然我们拥有丰富的经验，但仍需要您的配合和理解。
    </Fragment>

    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-slate-800"></div>
    </Fragment>
  </Content>




</Layout>

<style>
  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 transition-all duration-300;
  }
</style>
